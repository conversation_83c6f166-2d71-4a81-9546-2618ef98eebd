<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校招 - 梁琦坤 - 2025.08.11</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .interview-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .interview-info div {
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content {
            padding: 30px;
        }
        
        /* 导航栏样式 */
        .navigation {
            position: sticky;
            top: 0;
            background: white;
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: 2px solid #4facfe;
            z-index: 1000;
        }
        
        .nav-sections {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .nav-btn {
            padding: 8px 16px;
            background: #f8f9ff;
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #666;
            position: relative;
            overflow: hidden;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
            transform: translateY(-2px);
        }
        
        .nav-btn::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #4facfe;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .nav-btn.active::after,
        .nav-btn:hover::after {
            width: 100%;
        }
        
        .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .candidate-info {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .candidate-input {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            min-width: 150px;
            transition: all 0.3s ease;
        }
        
        .candidate-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .score-display {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            padding: 12px 24px;
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            border-radius: 25px;
            min-width: 140px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        
        .progress-container {
            margin-top: 15px;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9em;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e1e8ed;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 5px;
        }
        
        /* 章节样式 */
        .section {
            margin-bottom: 40px;
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #4facfe;
            scroll-margin-top: 120px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e8ed;
        }
        
        .section-subtitle {
            color: #4facfe;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-subtitle::before {
            content: "▶";
            font-size: 0.8em;
        }
        
        /* 问题样式 */
        .question {
            margin-bottom: 25px;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        
        .question:hover {
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .question-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #4facfe;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            font-weight: bold;
            flex-shrink: 0;
            transition: transform 0.2s ease;
        }
        
        .question:hover .question-number {
            transform: scale(1.1);
        }
        
        .question-text {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
            line-height: 1.5;
        }

        .answer-reference {
            font-size: 0.9em;
            color: #666;
            background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
            border-radius: 8px;
            margin-bottom: 12px;
            border-left: 3px solid #4facfe;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .answer-reference-header {
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(79, 172, 254, 0.05);
            border-bottom: 1px solid rgba(79, 172, 254, 0.1);
        }

        .answer-reference-header:hover {
            background: rgba(79, 172, 254, 0.08);
        }

        .answer-reference-title {
            font-weight: 600;
            color: #4facfe;
            font-size: 0.85em;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .answer-reference-toggle {
            font-size: 0.8em;
            color: #999;
            transition: transform 0.3s ease;
        }

        .answer-reference.expanded .answer-reference-toggle {
            transform: rotate(180deg);
        }

        .answer-reference-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .answer-reference.expanded .answer-reference-content {
            max-height: 1000px;
            padding: 12px 15px;
        }

        .answer-reference strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 6px;
        }

        .input-group {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .answer-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1em;
            min-height: 100px;
            resize: vertical;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .answer-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
        }

        .score-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            min-width: 80px;
        }

        .score-input {
            width: 70px;
            height: 50px;
            padding: 8px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .score-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            transform: scale(1.05);
        }

        .score-input.invalid {
            border-color: #ff6b6b;
            background-color: #ffe0e0;
        }

        .score-label {
            font-size: 0.85em;
            color: #666;
            text-align: center;
            font-weight: 600;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .input-group {
                flex-direction: column;
                gap: 15px;
            }

            .score-group {
                flex-direction: row;
                justify-content: center;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>校招 - 梁琦坤 - 2025.08.11</h1>
            <div class="interview-info">
                <div><strong>👨‍💼 面试官：</strong>左浩</div>
                <div><strong>⏱️ 面试时长：</strong>60分钟</div>
                <div><strong>💻 面试方式：</strong>远程视频面试</div>
            </div>
        </div>
        
        <div class="content">
            <div class="navigation">
                <div class="nav-sections">
                    <a href="#section-opening" class="nav-btn">开场&自我介绍</a>
                    <a href="#section-frontend" class="nav-btn">前端基础知识</a>
                    <a href="#section-project" class="nav-btn">项目经验深入</a>
                    <a href="#section-engineering" class="nav-btn">工程化与工具</a>
                    <a href="#section-qa" class="nav-btn">问答环节</a>
                </div>
                <div class="controls-row">
                    <div class="candidate-info">
                        <label>候选人姓名：</label>
                        <input type="text" id="candidateName" class="candidate-input" placeholder="梁琦坤" required>
                        <label>面试日期：</label>
                        <input type="date" id="interviewDate" class="candidate-input" required>
                    </div>
                    <div class="score-display">
                        平均分：<span id="averageScore">0.0</span>/5.0
                    </div>
                    <div class="action-buttons">
                        <button onclick="autoSave()" class="btn btn-secondary">💾 保存进度</button>
                        <button onclick="exportToExcel()" class="btn btn-primary">📊 导出Excel</button>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-info">
                        <span>完成进度</span>
                        <span id="progressText">0/26 题</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <!-- 开场 & 自我介绍 -->
            <div class="section" id="section-opening">
                <h2 class="section-title">🎯 开场 & 自我介绍（5分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">1</div>
                        <div class="question-text">面试官开场白（1分钟）</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 参考内容
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>参考内容：</strong>你好，梁同学！很高兴见到你，我是今天的面试官左浩。谢谢你抽时间来聊，咱们今天就轻松一点，当作一次技术交流。准备好了就开始吧。
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人反应和回应..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">2</div>
                        <div class="question-text">候选人自我介绍（4分钟）- 请先做个自我介绍、以及为何会选择前端开发</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                📝 评分要点
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>评分要点：</strong>表达逻辑清晰、技术背景介绍、项目经验相关性、个人特点突出
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的自我介绍内容..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二部分：前端基础知识 -->
            <div class="section" id="section-frontend">
                <h2 class="section-title">💻 第二部分：前端基础知识（25分钟）</h2>

                <h3 class="section-subtitle">JavaScript基础（10分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">3</div>
                        <div class="question-text">JavaScript有哪些数据类型？如何判断数据类型？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>基本数据类型：</strong>Number、String、Boolean、Undefined、Null、Symbol、BigInt<br>
                            <strong>引用数据类型：</strong>Object（包括Array、Function、Date等）<br>
                            <strong>类型判断方法：</strong><br>
                            • typeof: 适用于基本类型（注意typeof null === 'object'）<br>
                            • instanceof: 判断对象类型<br>
                            • Object.prototype.toString.call(): 最准确的方法<br>
                            • Array.isArray(): 专门判断数组<br>
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">4</div>
                        <div class="question-text">解释一下原型链，prototype和__proto__的区别？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>原型链：</strong>每个对象都有一个__proto__属性指向其构造函数的prototype，形成链式结构用于属性查找<br>
                            <strong>prototype：</strong>函数特有的属性，指向原型对象，包含共享的属性和方法<br>
                            <strong>__proto__：</strong>对象的属性，指向构造函数的prototype<br>
                            <strong>查找机制：</strong>对象访问属性时，先查找自身，找不到沿着__proto__向上查找直到Object.prototype
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">5</div>
                        <div class="question-text">Promise、async/await的区别和使用场景？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>Promise：</strong><br>
                            • 解决回调地狱问题<br>
                            • 三种状态：pending、fulfilled、rejected<br>
                            • 支持链式调用，通过.then()和.catch()处理<br>
                            • 适合单个异步操作或简单的链式操作<br><br>
                            <strong>async/await：</strong><br>
                            • 基于Promise的语法糖，让异步代码看起来像同步代码<br>
                            • async函数返回Promise<br>
                            • await只能在async函数中使用<br>
                            • 适合复杂的异步流程控制
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">6</div>
                        <div class="question-text">你提到用fetch API，能说说fetch和XMLHttpRequest的区别吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>fetch优势：</strong><br>
                            • 基于Promise，支持async/await<br>
                            • 语法更简洁现代<br>
                            • 支持Request/Response对象<br>
                            • 更好的错误处理机制<br>
                            • 支持流式处理<br><br>
                            <strong>XMLHttpRequest特点：</strong><br>
                            • 基于回调函数<br>
                            • 浏览器兼容性更好<br>
                            • 支持更多配置选项<br>
                            • 可以监听上传进度<br><br>
                            <strong>使用场景：</strong><br>
                            • 现代项目优先使用fetch<br>
                            • 需要上传进度监听时使用XMLHttpRequest<br>
                            • 兼容老浏览器时使用XMLHttpRequest
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Vue.js框架知识 -->
            <div class="section" id="section-vue">
                <h2 class="section-title">⚛️ Vue.js框架知识（10分钟）</h2>



                <div class="question">
                    <div class="question-header">
                        <div class="question-number">7</div>
                        <div class="question-text">Vue3的响应式原理是什么？和Vue2有什么区别？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>Vue2：</strong>使用Object.defineProperty劫持对象属性<br>
                            • 无法检测数组索引和长度变化<br>
                            • 无法检测对象属性的添加和删除<br>
                            • 需要递归遍历所有属性<br><br>
                            <strong>Vue3：</strong>使用Proxy代理整个对象<br>
                            • 可以检测所有类型的变化<br>
                            • 性能更好，惰性初始化<br>
                            • 支持Map、Set、Array等更多数据类型
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">8</div>
                        <div class="question-text">Vue3的生命周期钩子有哪些？onMounted和onUnmounted的使用场景？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>Vue3生命周期钩子：</strong><br>
                            • onBeforeMount: 组件挂载前<br>
                            • onMounted: 组件挂载后<br>
                            • onBeforeUpdate: 组件更新前<br>
                            • onUpdated: 组件更新后<br>
                            • onBeforeUnmount: 组件卸载前<br>
                            • onUnmounted: 组件卸载后<br><br>
                            <strong>onMounted使用场景：</strong><br>
                            • DOM操作<br>
                            • 第三方库初始化<br>
                            • 数据请求<br>
                            • 事件监听器添加<br><br>
                            <strong>onUnmounted使用场景：</strong><br>
                            • 清理定时器<br>
                            • 移除事件监听器<br>
                            • 取消网络请求<br>
                            • 清理第三方库实例
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">9</div>
                        <div class="question-text">组件销毁时需要注意什么？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>内存泄漏防止：</strong><br>
                            • 清理定时器和延时器<br>
                            • 移除事件监听器<br>
                            • 取消未完成的网络请求<br>
                            • 销毁第三方库实例<br><br>
                            <strong>资源清理：</strong><br>
                            • 清理WebSocket连接<br>
                            • 停止动画<br>
                            • 清理观察者模式的订阅
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">10</div>
                        <div class="question-text">Vue中如何进行性能优化？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>代码层面：</strong><br>
                            • 使用v-memo缓存模板<br>
                            • 合理使用computed和watch<br>
                            • 避免在模板中使用复杂表达式<br>
                            • 使用异步组件和懒加载<br><br>
                            <strong>构建优化：</strong><br>
                            • 代码分割和懒加载<br>
                            • Tree-shaking移除未使用代码<br>
                            • 组件库按需引入<br><br>
                            <strong>运行时优化：</strong><br>
                            • 使用Object.freeze冻结不变数据<br>
                            • 避免不必要的组件更新<br>
                            • 使用虚拟滚动处理大列表
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">11</div>
                        <div class="question-text">v-if和v-show的区别和使用场景？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>v-if：</strong><br>
                            • 条件性渲染，false时元素不会渲染到DOM<br>
                            • 有更高的切换开销<br>
                            • 适合条件很少改变的场景<br>
                            • 支持template标签<br><br>
                            <strong>v-show：</strong><br>
                            • 始终渲染，通过CSS display属性控制显示<br>
                            • 有更高的初始渲染开销<br>
                            • 适合需要频繁切换的场景<br>
                            • 不支持template标签<br><br>
                            <strong>使用场景：</strong><br>
                            • 权限控制、路由条件等用v-if<br>
                            • 标签页切换、模态框等用v-show
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">TypeScript知识（5分钟）</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">12</div>
                        <div class="question-text">TypeScript的优势是什么？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>类型安全：</strong>编译时发现错误，减少运行时错误<br>
                            <strong>更好的IDE支持：</strong>智能提示、重构、导航<br>
                            <strong>代码可读性：</strong>类型即文档，增强代码可维护性<br>
                            <strong>团队协作：</strong>统一的类型规范，降低沟通成本<br>
                            <strong>渐进式：</strong>可以逐步添加类型，兼容JavaScript
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">13</div>
                        <div class="question-text">interface和type的区别？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>interface特点：</strong><br>
                            • 只能定义对象类型<br>
                            • 支持声明合并<br>
                            • 支持继承（extends）<br>
                            • 更适合定义对象结构<br><br>
                            <strong>type特点：</strong><br>
                            • 可以定义任何类型（联合类型、交叉类型等）<br>
                            • 不支持声明合并<br>
                            • 支持计算属性<br>
                            • 更灵活，功能更强大
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">14</div>
                        <div class="question-text">泛型的使用场景？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>函数泛型：</strong>保持类型安全的同时提供灵活性<br>
                            <strong>组件泛型：</strong>Vue组件的props类型定义<br>
                            <strong>工具类型：</strong>创建可重用的类型操作
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三部分：项目经验深入 -->
            <div class="section" id="section-project">
                <h2 class="section-title">💼 第三部分：项目经验深入（20分钟）</h2>

                <h3 class="section-subtitle">针对ELIS系统开发项目</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">15</div>
                        <div class="question-text">为什么选择Vue3+Vite+TypeScript这个技术组合？相比Vue2有什么优势？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>Vue3优势：</strong><br>
                            • Composition API提供更好的逻辑复用<br>
                            • 性能提升（更小的包体积，更快的渲染）<br>
                            • 更好的TypeScript支持<br>
                            • Fragment支持，template可以有多个根节点<br><br>
                            <strong>Vite优势：</strong><br>
                            • 冷启动速度快（基于ESM）<br>
                            • 热更新效率高<br>
                            • 开箱即用的TypeScript支持<br>
                            • 更好的开发体验<br><br>
                            <strong>TypeScript优势：</strong><br>
                            • 类型安全，减少运行时错误<br>
                            • 更好的IDE支持和代码提示<br>
                            • 大型项目的可维护性
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>


                <div class="question">
                    <div class="question-header">
                        <div class="question-number">16</div>
                        <div class="question-text">你提到"支持主窗口与多个子窗口的灵活管理"，这个功能是怎么实现的？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 技术实现思路
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>窗口管理状态：</strong><br>
                            • 维护窗口列表和活跃窗口ID<br>
                            • 管理窗口的位置、大小、层级<br>
                            • 处理窗口的创建、销毁、最小化、最大化<br><br>
                            <strong>核心功能：</strong><br>
                            • createWindow: 创建新窗口<br>
                            • destroyWindow: 销毁窗口<br>
                            • 窗口拖拽和缩放<br>
                            • 层级管理（z-index）<br><br>
                            <strong>内存管理：</strong><br>
                            • 窗口关闭时清理组件实例<br>
                            • 移除事件监听器<br>
                            • 清理定时器和异步任务
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">17</div>
                        <div class="question-text">动态创建、销毁窗口时，如何处理内存管理和性能优化？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>内存管理：</strong><br>
                            • 窗口关闭时清理组件实例<br>
                            • 移除事件监听器<br>
                            • 清理定时器和异步任务<br>
                            • 使用WeakMap存储窗口相关数据<br><br>
                            <strong>性能优化：</strong><br>
                            • 窗口最小化时暂停不必要的更新<br>
                            • 使用虚拟滚动处理大量数据<br>
                            • 窗口池复用减少创建销毁开销<br>
                            • 懒加载窗口内容
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">18</div>
                        <div class="question-text">组合式API中的ref、reactive有什么区别？什么时候用哪个？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>ref：</strong><br>
                            • 用于基本数据类型和单个值<br>
                            • 通过.value访问和修改<br>
                            • 可以重新赋值整个对象<br><br>
                            <strong>reactive：</strong><br>
                            • 用于对象和数组<br>
                            • 直接访问属性，不需要.value<br>
                            • 不能重新赋值整个对象，会失去响应性<br><br>
                            <strong>使用建议：</strong><br>
                            • 基本类型用ref<br>
                            • 对象类型优先考虑reactive，除非需要整体替换<br>
                            • 组件props用ref包装后使用
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">19</div>
                        <div class="question-text">Vue Router的路由守卫有哪几种？你在项目中用了哪些？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>全局守卫：</strong><br>
                            • beforeEach: 全局前置守卫<br>
                            • beforeResolve: 全局解析守卫<br>
                            • afterEach: 全局后置钩子<br><br>
                            <strong>路由独享守卫：</strong><br>
                            • beforeEnter: 路由配置中定义<br><br>
                            <strong>组件内守卫：</strong><br>
                            • beforeRouteEnter: 进入路由前<br>
                            • beforeRouteUpdate: 路由更新时<br>
                            • beforeRouteLeave: 离开路由前<br><br>
                            <strong>常用场景：</strong><br>
                            • 权限验证<br>
                            • 登录状态检查<br>
                            • 页面访问控制
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">20</div>
                        <div class="question-text">前端权限控制的实现思路是什么？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>路由级权限：</strong>通过路由守卫控制页面访问<br>
                            <strong>组件级权限：</strong>通过指令或组件控制UI显示<br>
                            <strong>接口级权限：</strong>请求拦截器添加token，响应拦截器处理401<br><br>
                            <strong>实现方式：</strong><br>
                            • 权限指令：v-permission<br>
                            • 权限组件：根据权限显示/隐藏<br>
                            • 动态路由：根据权限动态添加路由<br>
                            • 菜单控制：根据权限显示菜单项
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">针对AI Agent项目</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">21</div>
                        <div class="question-text">你在这个项目中主要负责什么前端工作？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 期望答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>主要工作：</strong><br>
                            • 聊天界面开发<br>
                            • 流式数据渲染<br>
                            • 消息状态管理<br>
                            • 文件上传和预览功能<br><br>
                            <strong>技术挑战：</strong><br>
                            • 实时数据流处理<br>
                            • 用户体验优化<br>
                            • 错误处理和重试机制
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">22</div>
                        <div class="question-text">SSE流式响应是什么？为什么要用这种方式？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>SSE (Server-Sent Events)：</strong><br>
                            • 服务器向客户端推送数据的技术<br>
                            • 基于HTTP协议，单向通信<br>
                            • 支持自动重连和事件类型<br><br>
                            <strong>使用原因：</strong><br>
                            • AI生成内容需要逐步展示<br>
                            • 提升用户体验，避免长时间等待<br>
                            • 减少服务器压力，避免长连接阻塞<br><br>
                            <strong>实现要点：</strong><br>
                            • EventSource API<br>
                            • 错误处理和重连机制<br>
                            • 数据解析和UI更新
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第四部分：工程化与工具 -->
            <div class="section" id="section-engineering">
                <h2 class="section-title">🔧 第四部分：工程化与工具（8分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">23</div>
                        <div class="question-text">Vite相比Webpack有什么优势？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>开发体验：</strong><br>
                            • 冷启动速度更快（基于ESM）<br>
                            • 热更新效率更高<br>
                            • 内置TypeScript支持<br><br>
                            <strong>构建性能：</strong><br>
                            • 使用Rollup打包，体积更小<br>
                            • Tree-shaking效果更好<br>
                            • 并行构建支持<br><br>
                            <strong>配置简化：</strong><br>
                            • 开箱即用，配置更少<br>
                            • 插件生态丰富<br>
                            • 现代化的默认配置<br><br>
                            <strong>使用场景：</strong><br>
                            • 新项目优先选择Vite<br>
                            • Vue3/React等现代框架项目<br>
                            • 需要快速开发原型的场景
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">24</div>
                        <div class="question-text">前端项目的打包优化策略有哪些？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>代码分割：</strong><br>
                            • 路由懒加载<br>
                            • 第三方库单独打包<br>
                            • 动态import()<br><br>
                            <strong>资源优化：</strong><br>
                            • 图片压缩和WebP格式<br>
                            • CSS/JS压缩<br>
                            • Gzip压缩<br><br>
                            <strong>缓存策略：</strong><br>
                            • 文件名hash化<br>
                            • 合理设置缓存头<br>
                            • CDN部署
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>



                <div class="question">
                    <div class="question-header">
                        <div class="question-number">25</div>
                        <div class="question-text">Git的工作流程和分支管理策略？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 标准答案
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>Git Flow：</strong><br>
                            • master: 生产分支<br>
                            • develop: 开发分支<br>
                            • feature/*: 功能分支<br>
                            • release/*: 发布分支<br>
                            • hotfix/*: 热修复分支<br><br>
                            <strong>工作流程：</strong><br>
                            1. 从develop创建feature分支<br>
                            2. 完成功能后提交PR<br>
                            3. 代码审查通过后合并到develop<br>
                            4. 测试通过后合并到master发布<br><br>
                            <strong>最佳实践：</strong><br>
                            • 提交信息规范化<br>
                            • 小步快跑，频繁提交<br>
                            • 合并前rebase保持历史整洁
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第五部分：问答环节 -->
            <div class="section" id="section-qa">
                <h2 class="section-title">❓ 第五部分：问答环节（2分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">26</div>
                        <div class="question-text">你有什么想问我的吗？</div>
                    </div>
                    <div class="answer-reference">
                        <div class="answer-reference-header" onclick="toggleReference(this)">
                            <div class="answer-reference-title">
                                💡 考察重点
                            </div>
                            <div class="answer-reference-toggle">▼</div>
                        </div>
                        <div class="answer-reference-content">
                            <strong>考察重点：</strong><br>
                            • 对公司和岗位的关注程度<br>
                            • 职业规划和学习态度<br>
                            • 沟通能力<br><br>
                            <strong>好的问题示例：</strong><br>
                            • 团队技术栈和发展方向<br>
                            • 项目的技术挑战<br>
                            • 团队协作方式<br>
                            • 个人成长机会
                        </div>
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的问题和表现..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自动保存功能
        function autoSave() {
            const data = {
                candidateName: document.getElementById('candidateName').value,
                interviewDate: document.getElementById('interviewDate').value,
                answers: [],
                scores: []
            };

            // 收集所有答案和分数
            const answerInputs = document.querySelectorAll('.answer-input');
            const scoreInputs = document.querySelectorAll('.score-input');

            answerInputs.forEach((input, index) => {
                data.answers[index] = input.value;
            });

            scoreInputs.forEach((input, index) => {
                data.scores[index] = input.value;
            });

            // 保存到localStorage
            localStorage.setItem('interview_data_liangqikun', JSON.stringify(data));
            console.log('数据已保存到本地！');
        }

        // 加载保存的数据
        function loadSavedData() {
            const savedData = localStorage.getItem('interview_data_liangqikun');
            if (savedData) {
                const data = JSON.parse(savedData);

                if (data.candidateName) {
                    document.getElementById('candidateName').value = data.candidateName;
                }
                if (data.interviewDate) {
                    document.getElementById('interviewDate').value = data.interviewDate;
                }

                // 恢复答案
                const answerInputs = document.querySelectorAll('.answer-input');
                answerInputs.forEach((input, index) => {
                    if (data.answers && data.answers[index]) {
                        input.value = data.answers[index];
                    }
                });

                // 恢复分数
                const scoreInputs = document.querySelectorAll('.score-input');
                scoreInputs.forEach((input, index) => {
                    if (data.scores && data.scores[index]) {
                        input.value = data.scores[index];
                    }
                });

                calculateAverage();
            }
        }

        // 计算平均分
        function calculateAverage() {
            const scoreInputs = document.querySelectorAll('.score-input');
            let total = 0;
            let count = 0;

            scoreInputs.forEach(input => {
                const value = parseFloat(input.value);
                if (!isNaN(value) && value > 0) {
                    total += value;
                    count++;
                }
            });

            const average = count > 0 ? (total / count).toFixed(1) : '0.0';
            document.getElementById('averageScore').textContent = average;

            // 更新进度
            const totalQuestions = scoreInputs.length;
            const completedQuestions = count;
            const progressPercent = (completedQuestions / totalQuestions) * 100;

            document.getElementById('progressText').textContent = `${completedQuestions}/${totalQuestions} 题`;
            document.getElementById('progressFill').style.width = `${progressPercent}%`;
        }

        // 验证分数输入
        function validateScore(input) {
            const value = parseFloat(input.value);
            if (isNaN(value) || value < 1 || value > 5) {
                input.classList.add('invalid');
            } else {
                input.classList.remove('invalid');
            }
        }

        // 切换参考答案显示
        function toggleReference(header) {
            const reference = header.parentElement;
            reference.classList.toggle('expanded');
        }

        // 导出Excel功能
        function exportToExcel() {
            const candidateName = document.getElementById('candidateName').value || '梁琦坤';
            const interviewDate = document.getElementById('interviewDate').value || new Date().toISOString().split('T')[0];

            // 收集数据
            const data = [];
            const questions = document.querySelectorAll('.question');

            questions.forEach((question, index) => {
                const questionText = question.querySelector('.question-text').textContent;
                const answer = question.querySelector('.answer-input').value;
                const score = question.querySelector('.score-input').value;

                data.push({
                    '题号': index + 1,
                    '问题': questionText,
                    '候选人回答': answer,
                    '分数': score
                });
            });

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(data);

            // 设置列宽
            ws['!cols'] = [
                { width: 8 },   // 题号
                { width: 50 },  // 问题
                { width: 60 },  // 回答
                { width: 10 }   // 分数
            ];

            XLSX.utils.book_append_sheet(wb, ws, '面试记录');

            // 导出文件
            const fileName = `面试记录_${candidateName}_${interviewDate}.xlsx`;
            XLSX.writeFile(wb, fileName);
        }

        // 导航功能
        function initNavigation() {
            const navBtns = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.section');

            // 点击导航按钮
            navBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = btn.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // 滚动时更新导航状态
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 150;
                    if (window.pageYOffset >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });

                navBtns.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.getAttribute('href') === `#${current}`) {
                        btn.classList.add('active');
                    }
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedData();
            initNavigation();

            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            if (!document.getElementById('interviewDate').value) {
                document.getElementById('interviewDate').value = today;
            }

            // 自动保存
            setInterval(autoSave, 30000); // 每30秒自动保存一次
        });
    </script>
</body>
</html>
